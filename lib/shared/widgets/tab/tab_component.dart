import 'package:flutter/material.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';

import '../../theme/color_pallette.dart';
import '../../theme/font_pallette.dart';

class TabComponent extends StatelessWidget {
  final String label1;
  final String label2;
  final String? label3;
  final String? label4;
  final int selectedIndex;
  final ValueChanged<int> onTabSelected;
  final Widget? customWidget;

  const TabComponent({
    super.key,
    required this.label1,
    required this.label2,
    this.label3,
    this.label4,
    required this.selectedIndex,
    required this.onTabSelected,
    this.customWidget,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        _buildTab(0, label1),
        35.horizontalSpace, // Responsive spacing
        _buildTab(1, label2),
        if (label3 != null) ...[
          35.horizontalSpace,
          _buildTab(2, label3!),
        ],
        if (label4 != null) ...[
          35.horizontalSpace,
          _buildTab(3, label4!),
        ],
      ],
    );
  }

  Widget _buildTab(int index, String label) {
    return index == selectedIndex
        ? _SelectedTab(label: label)
        : _UnselectedTab(
            label: label,
            onTap: () => onTabSelected(index),
            customWidget: customWidget,
          );
  }
}

class _SelectedTab extends StatelessWidget {
  final String label;

  const _SelectedTab({
    required this.label,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          alignment: Alignment.center,
          margin: EdgeInsets.all(4.gw),
          child: AnimationConfiguration.synchronized(
            duration: const Duration(milliseconds: 300),
            child: ScaleAnimation(
              scale: 1.05,
              child: Text(
                label,
                style: FontPalette.medium16.copyWith(
                  color: context.theme.primaryColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
        ),
        AnimationConfiguration.synchronized(
          duration: const Duration(milliseconds: 400),
          child: SlideAnimation(
            horizontalOffset: 20.0,
            child: FadeInAnimation(
              child: Container(
                height: 2.gh,
                width: 50.gw,
                color: context.theme.primaryColor,
              ),
            ),
          ),
        ),
      ],
    );
  }
}

class _UnselectedTab extends StatefulWidget {
  final String label;
  final VoidCallback onTap;
  final Widget? customWidget;

  const _UnselectedTab({
    required this.label,
    required this.onTap,
    this.customWidget,
  });

  @override
  State<_UnselectedTab> createState() => _UnselectedTabState();
}

class _UnselectedTabState extends State<_UnselectedTab> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(begin: 1.0, end: 0.95).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: (_) => _controller.forward(),
      onTapUp: (_) {
        _controller.reverse();
        widget.onTap();
      },
      onTapCancel: () => _controller.reverse(),
      child: ScaleTransition(
        scale: _scaleAnimation,
        child: Center(
          child: widget.customWidget ??
              Text(
                widget.label,
                style: FontPalette.medium16.copyWith(
                  color: ColorPalette.titleColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
        ),
      ),
    );
  }
}
