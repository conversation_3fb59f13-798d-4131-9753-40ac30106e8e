import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/theme/color_pallette.dart';
import 'package:gp_stock_app/shared/theme/font_pallette.dart';

class CustomTabBar extends StatelessWidget {
  final List<String> tabs;
  final int selectedIndex;
  final Function(int) onTabSelected;
  final EdgeInsets? padding;
  final bool showIndicator;
  final Color? selectedColor;
  final Color? unselectedColor;
  final TextStyle? selectedTextStyle;
  final TextStyle? unselectedTextStyle;
  final double? height;
  final bool showDivider;

  const CustomTabBar({
    super.key,
    required this.tabs,
    required this.selectedIndex,
    required this.onTabSelected,
    this.padding,
    this.showIndicator = true,
    this.selectedColor,
    this.unselectedColor,
    this.selectedTextStyle,
    this.unselectedTextStyle,
    this.height,
    this.showDivider = true,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 12.gw),
      child: Container(
        height: height ?? 30.gh,
        decoration: BoxDecoration(
          color: context.theme.cardColor,
          borderRadius: BorderRadius.circular(20.gr),
          border: showDivider
              ? Border(
                  bottom: BorderSide(
                    color: context.theme.dividerColor,
                    width: 1,
                  ),
                )
              : null,
        ),
        child: Row(
          children: List.generate(
            tabs.length,
            (index) => Expanded(
              child: _buildTab(context, index),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTab(BuildContext context, int index) {
    final isSelected = index == selectedIndex;
    final effectiveSelectedColor = selectedColor ?? context.theme.primaryColor;
    final effectiveUnselectedColor = unselectedColor ?? context.colorTheme.textRegular;

    return InkWell(
      onTap: () => onTabSelected(index),
      child: Container(
        height: height ?? 30.gh,
        padding: padding ?? EdgeInsets.symmetric(horizontal: 10.gw),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20.gr),
          color: isSelected ? effectiveSelectedColor : context.theme.cardColor,
        ),
        child: Center(
          child: Text(
            tabs[index],
            textAlign: TextAlign.center,
            style: (isSelected ? selectedTextStyle : unselectedTextStyle) ??
                FontPalette.medium14.copyWith(
                  color: isSelected ? ColorPalette.whiteColor : effectiveUnselectedColor,
                ),
          ),
        ),
      ),
    );
  }
}
